<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="4">
            <item index="0" class="java.lang.String" itemvalue="pydantic" />
            <item index="1" class="java.lang.String" itemvalue="pytest" />
            <item index="2" class="java.lang.String" itemvalue="google-api-python-client" />
            <item index="3" class="java.lang.String" itemvalue="google-auth" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>