<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;4ff2555c-862e-4fe7-81ce-3e5b6fbd4399&quot;,&quot;conversations&quot;:{&quot;f3eeb6a0-491e-4e0d-8333-9d9fd63d2a7d&quot;:{&quot;id&quot;:&quot;f3eeb6a0-491e-4e0d-8333-9d9fd63d2a7d&quot;,&quot;createdAtIso&quot;:&quot;2025-06-10T14:06:03.033Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-10T14:06:03.033Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;run the &quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;run the &quot;}]}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;/Users/<USER>/Documents/Make_my_days_landing_page/app/page.tsx&quot;,&quot;/Users/<USER>/Documents/Make_my_days_landing_pagefalsefalse&quot;,&quot;userGuidelines&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;009f9802-c7dd-4437-9953-87908c2778cf&quot;},&quot;4ff2555c-862e-4fe7-81ce-3e5b6fbd4399&quot;:{&quot;id&quot;:&quot;4ff2555c-862e-4fe7-81ce-3e5b6fbd4399&quot;,&quot;createdAtIso&quot;:&quot;2025-06-10T14:06:39.672Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-10T14:07:04.523Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;/Users/<USER>/Documents/Make_my_days_landing_page/app/page.tsx&quot;,&quot;/Users/<USER>/Documents/Make_my_days_landing_pagefalsefalse&quot;,&quot;userGuidelines&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;1015fcaa-6b2c-4314-ab4c-af590c87d35c&quot;},&quot;30410e13-70df-4b9f-b3f7-fea9414a3231&quot;:{&quot;id&quot;:&quot;30410e13-70df-4b9f-b3f7-fea9414a3231&quot;,&quot;createdAtIso&quot;:&quot;2025-06-10T14:06:39.676Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-10T14:06:39.676Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;run the &quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;run the &quot;}]}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;draftActiveContextIds&quot;:[&quot;/Users/<USER>/Documents/Make_my_days_landing_page/app/page.tsx&quot;,&quot;/Users/<USER>/Documents/Make_my_days_landing_pagefalsefalse&quot;,&quot;userGuidelines&quot;,&quot;agentMemories&quot;],&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;32bfb76f-b8b7-4649-87c6-69a0a0849b0a&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>