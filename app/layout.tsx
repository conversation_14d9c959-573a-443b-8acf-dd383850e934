import type { <PERSON>ada<PERSON> } from 'next'
import { <PERSON><PERSON> } from 'next/font/google'
import './globals.css'

const roboto = Roboto({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-roboto',
})

export const metadata: Metadata = {
  title: 'Make My Days - Plan your days in the ADHD way',
  description: 'An AI-enabled time orchestrator that revitalizes your reschedule according to reality without any pain. Developed by ADHDers, for ADHDers.',
  generator: 'v0.dev',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`${roboto.variable} font-roboto antialiased`}>{children}</body>
    </html>
  )
}
