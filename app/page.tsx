import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  CheckSquare,
  Smartphone,
  Puzzle,
  ArrowRight,
  Clock,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { AnimatedText } from "@/components/animated-text";

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <main className="flex-1">
        {/* Hero Section */}
        <section
          className="w-full bg-gradient-to-br from-primary/5 to-secondary/10 relative"
          style={{ minHeight: "880px" }}
        >
          <div className="container px-4 md:px-6 mx-auto h-full">
            <div className="flex flex-col justify-center items-center min-h-screen space-y-8 text-center max-w-4xl mx-auto">
              <div className="flex items-center mb-4">
                <Image
                  src="/images/logo.png"
                  alt="Make My Days Logo"
                  width={64}
                  height={64}
                  className="h-16 w-16"
                />
                <span className="ml-3 text-3xl font-bold text-gray-900">
                  Make My Days
                </span>
              </div>

              <div className="space-y-4 max-w-2xl">
                <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-semibold tracking-tight text-primary text-left leading-tight">
                  Plan your days in
                  <br />
                  the ADHD way
                </h1>
              </div>

              <div className="space-y-4 max-w-2xl">
                <p className="text-lg md:text-xl text-primary leading-relaxed font-medium text-left">
                  An AI-enabled time orchestrator that revitalizes your
                  reschedule according to reality without any pain.
                </p>
              </div>

              <div className="flex justify-center">
                <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3">
                  Join the Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Bottom tagline */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
              <p className="text-base md:text-lg text-primary font-medium">
                Developed by ADHDers, for ADHDers.
              </p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-16 md:py-24">
          <div className="container px-4 md:px-6 mx-auto max-w-[980px]">
            <div className="space-y-32">
              {/* Feature 1: Planning with AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      You can never make realistic plans?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <div className="flex items-center gap-3">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8"
                        />
                        <h4 className="text-2xl md:text-3xl font-bold text-gray-800">
                          Make My Days plans with you
                        </h4>
                      </div>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-400 leading-relaxed">
                          Make My Days's AI helps you create long-term plans
                          that you can actually stick to. Our AI will even ask
                          you to rethink when you propose overly optimistic
                          plans, guiding you toward realistic goals that work
                          with your brain, not against it.
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 -mb-4 -mx-4 md:-mb-12 md:-mx-12">
                        <Image
                          src="/images/plan_realistically.png"
                          width={483}
                          height={525}
                          alt="AI helping user plan realistic wake-up time goals"
                          className="rounded-t-lg w-full"
                        />
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>

              {/* Feature 2: Sub-tasking by AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      Your mind wanders and you forget what to do next?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <div className="flex items-center gap-3">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8"
                        />
                        <h4 className="text-2xl md:text-3xl font-bold text-gray-700">
                          Make My Days turns your thoughts to achievable steps
                        </h4>
                      </div>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-500 leading-relaxed">
                          Make My Days's AI transforms your general thoughts of
                          "doing something" into detailed, actionable steps. All
                          you need to do is confirm each step and check them off
                          as you complete them. No more wondering "what was I
                          supposed to do next?"
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 -mb-4 -mx-4 md:-mb-12 md:-mx-12">
                        <Image
                          src="/images/achievable_steps.png"
                          width={460}
                          height={528}
                          alt="Task breakdown from general idea to specific steps"
                          className="rounded-t-lg w-full"
                        />
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>

              {/* Feature 3: Dynamic Schedule by AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-primary">
                      You miss deadlines and blame yourself for that?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12">
                    <div className="space-y-6">
                      {/* Feature Title with Logo */}
                      <div className="flex items-center gap-3">
                        <Image
                          src="/images/logo.png"
                          alt="Make My Days Logo"
                          width={32}
                          height={32}
                          className="h-8 w-8"
                        />
                        <h4 className="text-2xl md:text-3xl font-bold text-gray-700">
                          Make My Days reschedules your todos
                        </h4>
                      </div>

                      {/* Feature Description */}
                      <div className="space-y-4">
                        <p className="text-lg text-gray-500 leading-relaxed">
                          Make My Days's AI can reschedule your entire day
                          without any stress or guilt. When life happens and
                          plans change, our AI instantly adapts your schedule to
                          reality, keeping you on track without the mental
                          overhead of replanning everything yourself.
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-8 text-center">
                        <Image
                          src="/images/reschedule.png"
                          width={451}
                          height={434}
                          alt="AI rescheduling task with smart suggestions"
                          className="rounded-lg mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>

              {/* Features 4 & 5: Parallel Feature Boxes */}
              <div className="grid lg:grid-cols-2 gap-8">
                {/* Feature 4: iPhone Sync */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8">
                    <div className="space-y-6">
                      {/* Feature Icon */}
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-full bg-primary/10">
                          <Smartphone className="h-6 w-6 text-primary" />
                        </div>
                      </div>

                      {/* Sub-headline */}
                      <h4 className="text-xl md:text-2xl font-bold text-gray-900">
                        Always in Your Pocket
                      </h4>

                      {/* Feature Description */}
                      <div className="space-y-3">
                        <p className="text-base text-gray-500 leading-relaxed">
                          Never forget to check your todo list again. Make My
                          Days syncs seamlessly with your iPhone calendar and
                          displays your next task right on your lock screen.
                        </p>
                        <p className="text-base text-gray-500 leading-relaxed">
                          Your tasks follow you everywhere, making it impossible
                          to lose track of what needs to be done next.
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-6 text-center">
                        <Image
                          src="/images/iphone-calendar.png"
                          width={200}
                          height={300}
                          alt="iPhone showing next task notification"
                          className="rounded-lg mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </AnimatedText>

                {/* Feature 5: Adaptive Methods */}
                <AnimatedText>
                  <div className="bg-[#F6F5F4] rounded-xl p-8">
                    <div className="space-y-6">
                      {/* Feature Icon */}
                      <div className="flex items-center gap-3">
                        <div className="p-3 rounded-full bg-secondary/10">
                          <Puzzle className="h-6 w-6 text-secondary" />
                        </div>
                      </div>

                      {/* Sub-headline */}
                      <h4 className="text-xl md:text-2xl font-bold text-gray-900">
                        Your Way, Your Methods
                      </h4>

                      {/* Feature Description */}
                      <div className="space-y-3">
                        <p className="text-base text-gray-500 leading-relaxed">
                          Every ADHDer has their own unique ways to cope and
                          thrive. Make My Days supports different productivity
                          strategies through customizable plugins.
                        </p>
                        <p className="text-base text-gray-500 leading-relaxed">
                          Whether you use Spoon Theory, Eat the Frog, or Weekly
                          Reviews, our system adapts to your preferred methods.
                        </p>
                      </div>

                      {/* Feature Image */}
                      <div className="mt-6 text-center">
                        <div className="grid grid-cols-3 gap-4 mb-4 justify-center">
                          <div className="w-16 h-16 bg-primary/20 rounded-lg flex items-center justify-center">
                            🥄
                          </div>
                          <div className="w-16 h-16 bg-secondary/20 rounded-lg flex items-center justify-center">
                            🐸
                          </div>
                          <div className="w-16 h-16 bg-tertiary/20 rounded-lg flex items-center justify-center">
                            📅
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedText>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section
          id="waitlist"
          className="w-full py-16 md:py-24 bg-gradient-to-r from-primary to-primary/80"
        >
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-8 text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white">
                Ready to Make Your Days?
              </h2>
              <p className="text-lg text-white/90">
                Join thousands of ADHDers who are taking control of their time
              </p>

              <div className="flex justify-center">
                <Button className="bg-white text-primary hover:bg-gray-100 px-8 py-3">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <p className="text-sm text-white/80">
                No spam, just updates on our launch. Unsubscribe anytime.
              </p>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full py-12 bg-gray-50 border-t">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-center">
                <Image
                  src="/images/logo.png"
                  alt="Make My Days Logo"
                  width={24}
                  height={24}
                  className="h-6 w-6"
                />
                <span className="ml-2 text-lg font-bold text-gray-900">
                  Make My Days
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Time management designed for the ADHD brain
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Connect</h4>
              <div className="space-y-2 text-sm">
                <Link
                  href="#waitlist"
                  className="block text-gray-600 hover:text-primary"
                >
                  Join Waitlist
                </Link>
                <Link
                  href="mailto:<EMAIL>"
                  className="block text-gray-600 hover:text-primary"
                >
                  Contact Us
                </Link>
                <Link
                  href="#"
                  className="block text-gray-600 hover:text-primary"
                >
                  Updates
                </Link>
              </div>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200 text-center">
            <p className="text-sm text-gray-600">
              © {new Date().getFullYear()} Make My Days. All rights reserved.
              Made with ❤️ by ADHDers, for ADHDers.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
