import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Calendar, CheckSquare, Smartphone, Puzzle, ArrowRight, Clock, Target, Zap } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-br from-primary/5 to-secondary/10">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-8 text-center max-w-4xl mx-auto">
              <Badge variant="outline" className="border-primary text-primary">
                AI-Powered Time Management
              </Badge>

              <div className="flex items-center mb-4">
                <Image src="/images/logo.png" alt="Make My Days Logo" width={64} height={64} className="h-16 w-16" />
                <span className="ml-3 text-3xl font-bold text-gray-900">Make My Days</span>
              </div>

              <div className="space-y-4">
                <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
                  <span className="text-primary">Make My Day</span>
                </h1>
                <p className="text-xl md:text-2xl text-gray-600 font-medium">plan your day in the ADHD way</p>
              </div>

              <div className="space-y-4 max-w-3xl">
                <p className="text-lg md:text-xl text-gray-700 leading-relaxed">
                  An AI-enabled time orchestrator that revitalizes your reschedule according to reality without any
                  pain.
                </p>
                <p className="text-base md:text-lg text-primary font-medium">developed by ADHDers, for ADHDers.</p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <Input type="email" placeholder="Enter your email" className="flex-1" />
                <Button className="bg-primary hover:bg-primary/90 text-white px-8">
                  Join the Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-16 md:py-24">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Built for the ADHD Brain</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Every feature is designed to work with your ADHD, not against it
              </p>
            </div>

            <div className="space-y-24">
              {/* Feature 1: Planning */}
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-full bg-primary/10">
                      <Calendar className="h-6 w-6 text-primary" />
                    </div>
                    <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground">
                      Planning
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl md:text-3xl font-bold text-gray-900">Long-term Planning Made Simple</h3>

                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <Target className="h-5 w-5 text-red-500 mt-1 flex-shrink-0" />
                        <p className="text-gray-600">
                          <strong>Problem:</strong> Some ADHDers struggle to make long-term plans
                        </p>
                      </div>

                      <div className="flex items-start gap-3">
                        <Zap className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                        <p className="text-gray-700">
                          <strong>Solution:</strong> Make My Days makes long-term and realistic plans for you.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-8 min-h-[300px] flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <Image
                      src="/images/plan-realistically.png"
                      width={400}
                      height={300}
                      alt="AI helping user plan realistic wake-up time goals"
                      className="rounded-lg"
                    />
                    <p className="mt-4 text-sm">AI suggesting gradual approach to wake up earlier</p>
                  </div>
                </div>
              </div>

              {/* Feature 2: Sub-tasking */}
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="bg-gray-50 rounded-xl p-8 min-h-[300px] flex items-center justify-center lg:order-first">
                  <div className="text-center text-gray-500">
                    <Image
                      src="/images/subtasking.png"
                      width={400}
                      height={300}
                      alt="Task breakdown from general idea to specific steps"
                      className="rounded-lg"
                    />
                    <p className="mt-4 text-sm">Blog writing task broken into actionable steps</p>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-full bg-secondary/10">
                      <CheckSquare className="h-6 w-6 text-secondary" />
                    </div>
                    <Badge variant="secondary" className="bg-tertiary/20 text-tertiary-foreground">
                      Sub-tasking
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl md:text-3xl font-bold text-gray-900">From Thoughts to Actions</h3>

                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <Target className="h-5 w-5 text-red-500 mt-1 flex-shrink-0" />
                        <p className="text-gray-600">
                          <strong>Problem:</strong> Some ADHDers have difficulties putting thoughts into actions
                        </p>
                      </div>

                      <div className="flex items-start gap-3">
                        <Zap className="h-5 w-5 text-secondary mt-1 flex-shrink-0" />
                        <p className="text-gray-700">
                          <strong>Solution:</strong> Make My Days turns your general thoughts of "doing something" into
                          detailed action items. All you need is to confirm and check each of them after finishing.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature 3: Dynamic Schedule */}
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-full bg-tertiary/10">
                      <Clock className="h-6 w-6 text-tertiary" />
                    </div>
                    <Badge variant="secondary" className="bg-primary/20 text-primary-foreground">
                      Dynamic Schedule
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl md:text-3xl font-bold text-gray-900">Smart Rescheduling</h3>

                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <Target className="h-5 w-5 text-red-500 mt-1 flex-shrink-0" />
                        <p className="text-gray-600">
                          <strong>Problem:</strong> Some ADHDers always change the plan or miss deadlines
                        </p>
                      </div>

                      <div className="flex items-start gap-3">
                        <Zap className="h-5 w-5 text-tertiary mt-1 flex-shrink-0" />
                        <p className="text-gray-700">
                          <strong>Solution:</strong> Make My Days can reschedule your days by AI. Smart, quick, no pain.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-8 min-h-[300px] flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <Image
                      src="/images/edit-plan.png"
                      width={400}
                      height={300}
                      alt="AI rescheduling task with smart suggestions"
                      className="rounded-lg"
                    />
                    <p className="mt-4 text-sm">AI rescheduling user system building task</p>
                  </div>
                </div>
              </div>

              {/* Feature 4: iPhone Sync */}
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="bg-gray-50 rounded-xl p-8 min-h-[300px] flex items-center justify-center lg:order-first">
                  <div className="text-center text-gray-500">
                    <Image
                      src="/images/iphone-calendar.png"
                      width={200}
                      height={300}
                      alt="iPhone showing next task notification"
                      className="rounded-lg"
                    />
                    <p className="mt-4 text-sm">Next todo appearing on iPhone screen</p>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-full bg-primary/10">
                      <Smartphone className="h-6 w-6 text-primary" />
                    </div>
                    <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground">
                      iPhone Sync
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl md:text-3xl font-bold text-gray-900">Always in Your Pocket</h3>

                    <p className="text-gray-700 text-lg">
                      Your next task appears right on your iPhone screen when you need it. No more forgetting what you
                      were supposed to do.
                    </p>
                  </div>
                </div>
              </div>

              {/* Feature 5: Methodologies */}
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-full bg-secondary/10">
                      <Puzzle className="h-6 w-6 text-secondary" />
                    </div>
                    <Badge variant="secondary" className="bg-tertiary/20 text-tertiary-foreground">
                      Adaptive Methods
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-2xl md:text-3xl font-bold text-gray-900">Your Way, Your Methods</h3>

                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <Target className="h-5 w-5 text-red-500 mt-1 flex-shrink-0" />
                        <p className="text-gray-600">
                          <strong>Problem:</strong> Every ADHDer has their own ways to cope
                        </p>
                      </div>

                      <div className="flex items-start gap-3">
                        <Zap className="h-5 w-5 text-secondary mt-1 flex-shrink-0" />
                        <p className="text-gray-700">
                          <strong>Solution:</strong> Make My Days supports different coping strategies by adding plugins
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-8 min-h-[300px] flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="w-16 h-16 bg-primary/20 rounded-lg flex items-center justify-center">🥄</div>
                      <div className="w-16 h-16 bg-secondary/20 rounded-lg flex items-center justify-center">🐸</div>
                      <div className="w-16 h-16 bg-tertiary/20 rounded-lg flex items-center justify-center">📅</div>
                    </div>
                    <p className="text-sm">Spoon Theory, Eat the Frog, Weekly Review & more</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="waitlist" className="w-full py-16 md:py-24 bg-gradient-to-r from-primary to-primary/80">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-8 text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white">Ready to Make Your Days?</h2>
              <p className="text-lg text-white/90">Join thousands of ADHDers who are taking control of their time</p>

              <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <Input type="email" placeholder="Enter your email" className="flex-1 bg-white" />
                <Button className="bg-white text-primary hover:bg-gray-100 px-8">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <p className="text-sm text-white/80">No spam, just updates on our launch. Unsubscribe anytime.</p>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full py-12 bg-gray-50 border-t">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-center">
                <Image src="/images/logo.png" alt="Make My Days Logo" width={24} height={24} className="h-6 w-6" />
                <span className="ml-2 text-lg font-bold text-gray-900">Make My Days</span>
              </div>
              <p className="text-sm text-gray-600">Time management designed for the ADHD brain</p>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Connect</h4>
              <div className="space-y-2 text-sm">
                <Link href="#waitlist" className="block text-gray-600 hover:text-primary">
                  Join Waitlist
                </Link>
                <Link href="mailto:<EMAIL>" className="block text-gray-600 hover:text-primary">
                  Contact Us
                </Link>
                <Link href="#" className="block text-gray-600 hover:text-primary">
                  Updates
                </Link>
              </div>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200 text-center">
            <p className="text-sm text-gray-600">
              © {new Date().getFullYear()} Make My Days. All rights reserved. Made with ❤️ by ADHDers, for ADHDers.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
