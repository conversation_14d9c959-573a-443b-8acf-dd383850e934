import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Calendar, CheckSquare, Smartphone, Puzzle, ArrowRight, Clock } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { AnimatedText } from "@/components/animated-text"

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-br from-primary/5 to-secondary/10">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-8 text-center max-w-4xl mx-auto">
              <Badge variant="outline" className="border-primary text-primary">
                AI-Powered Time Management
              </Badge>

              <div className="flex items-center mb-4">
                <Image src="/images/logo.png" alt="Make My Days Logo" width={64} height={64} className="h-16 w-16" />
                <span className="ml-3 text-3xl font-bold text-gray-900">Make My Days</span>
              </div>

              <div className="space-y-4">
                <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl text-primary">
                  Plan your days in the ADHD way
                </h1>
              </div>

              <div className="space-y-4 max-w-3xl">
                <p className="text-lg md:text-xl text-gray-700 leading-relaxed">
                  An AI-enabled time orchestrator that revitalizes your reschedule according to reality without any
                  pain.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <Input type="email" placeholder="Enter your email" className="flex-1" />
                <Button className="bg-primary hover:bg-primary/90 text-white px-8">
                  Join the Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <p className="text-base md:text-lg text-primary font-medium">Developed by ADHDers, for ADHDers.</p>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-16 md:py-24">
          <div className="container px-4 md:px-6 mx-auto">

            <div className="space-y-32">
              {/* Feature 1: Planning with AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900">
                      You can never make realistic plans?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 max-w-4xl mx-auto">
                  <div className="space-y-6 text-center">
                    {/* Feature Badge and Icon */}
                    <div className="flex items-center justify-center gap-3">
                      <div className="p-3 rounded-full bg-primary/10">
                        <Calendar className="h-6 w-6 text-primary" />
                      </div>
                      <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground">
                        Planning with AI
                      </Badge>
                    </div>

                    {/* Sub-headline */}
                    <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                      AI-Powered Realistic Planning
                    </h4>

                    {/* Feature Description */}
                    <div className="space-y-4 max-w-2xl mx-auto">
                      <p className="text-lg text-gray-700 leading-relaxed">
                        It's not your fault! ADHDers tend to view the future as one big box and don't distinguish between "in an hour," "tomorrow," "next week," or "in five years."
                      </p>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Make My Days's AI helps you create long-term plans that you can actually stick to. Our AI will even ask you to rethink when you propose overly optimistic plans, guiding you toward realistic goals that work with your brain, not against it.
                      </p>
                    </div>

                    {/* Feature Image */}
                    <div className="mt-8">
                      <Image
                        src="/images/plan-realistically.png"
                        width={400}
                        height={300}
                        alt="AI helping user plan realistic wake-up time goals"
                        className="rounded-lg mx-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature 2: Sub-tasking by AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText delay={200}>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900">
                      Your mind wanders and you forget what to do next?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 max-w-4xl mx-auto">
                  <div className="space-y-6 text-center">
                    {/* Feature Badge and Icon */}
                    <div className="flex items-center justify-center gap-3">
                      <div className="p-3 rounded-full bg-secondary/10">
                        <CheckSquare className="h-6 w-6 text-secondary" />
                      </div>
                      <Badge variant="secondary" className="bg-tertiary/20 text-tertiary-foreground">
                        Sub-tasking by AI
                      </Badge>
                    </div>

                    {/* Sub-headline */}
                    <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                      From Thoughts to Actionable Steps
                    </h4>

                    {/* Feature Description */}
                    <div className="space-y-4 max-w-2xl mx-auto">
                      <p className="text-lg text-gray-700 leading-relaxed">
                        It's not your fault! ADHDer's creativity comes from the same wandering minds that make focus challenging.
                      </p>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Make My Days's AI transforms your general thoughts of "doing something" into detailed, actionable steps. All you need to do is confirm each step and check them off as you complete them. No more wondering "what was I supposed to do next?"
                      </p>
                    </div>

                    {/* Feature Image */}
                    <div className="mt-8">
                      <Image
                        src="/images/subtasking.png"
                        width={400}
                        height={300}
                        alt="Task breakdown from general idea to specific steps"
                        className="rounded-lg mx-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature 3: Dynamic Schedule by AI */}
              <div className="space-y-16">
                {/* Problem Statement */}
                <div className="text-center py-16 md:py-24">
                  <AnimatedText delay={400}>
                    <h3 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900">
                      You miss deadlines and blame yourself for that?
                    </h3>
                  </AnimatedText>
                </div>

                {/* Feature Section */}
                <div className="bg-[#F6F5F4] rounded-xl p-8 md:p-12 max-w-4xl mx-auto">
                  <div className="space-y-6 text-center">
                    {/* Feature Badge and Icon */}
                    <div className="flex items-center justify-center gap-3">
                      <div className="p-3 rounded-full bg-tertiary/10">
                        <Clock className="h-6 w-6 text-tertiary" />
                      </div>
                      <Badge variant="secondary" className="bg-primary/20 text-primary-foreground">
                        Dynamic Schedule by AI
                      </Badge>
                    </div>

                    {/* Sub-headline */}
                    <h4 className="text-2xl md:text-3xl font-bold text-gray-900">
                      Effortless Smart Rescheduling
                    </h4>

                    {/* Feature Description */}
                    <div className="space-y-4 max-w-2xl mx-auto">
                      <p className="text-lg text-gray-700 leading-relaxed">
                        It's not your fault! Your brain just works differently and you should embrace it, not fight against it.
                      </p>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Make My Days's AI can reschedule your entire day without any stress or guilt. When life happens and plans change, our AI instantly adapts your schedule to reality, keeping you on track without the mental overhead of replanning everything yourself.
                      </p>
                    </div>

                    {/* Feature Image */}
                    <div className="mt-8">
                      <Image
                        src="/images/edit-plan.png"
                        width={400}
                        height={300}
                        alt="AI rescheduling task with smart suggestions"
                        className="rounded-lg mx-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Features 4 & 5: Parallel Feature Boxes */}
              <div className="grid lg:grid-cols-2 gap-8">
                {/* Feature 4: iPhone Sync */}
                <div className="bg-[#F6F5F4] rounded-xl p-8">
                  <div className="space-y-6 text-center">
                    {/* Feature Badge and Icon */}
                    <div className="flex items-center justify-center gap-3">
                      <div className="p-3 rounded-full bg-primary/10">
                        <Smartphone className="h-6 w-6 text-primary" />
                      </div>
                      <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground">
                        iPhone Sync
                      </Badge>
                    </div>

                    {/* Sub-headline */}
                    <h4 className="text-xl md:text-2xl font-bold text-gray-900">
                      Always in Your Pocket
                    </h4>

                    {/* Feature Description */}
                    <div className="space-y-3">
                      <p className="text-base text-gray-700 leading-relaxed">
                        Never forget to check your todo list again. Make My Days syncs seamlessly with your iPhone calendar and displays your next task right on your lock screen.
                      </p>
                      <p className="text-base text-gray-700 leading-relaxed">
                        Your tasks follow you everywhere, making it impossible to lose track of what needs to be done next.
                      </p>
                    </div>

                    {/* Feature Image */}
                    <div className="mt-6">
                      <Image
                        src="/images/iphone-calendar.png"
                        width={200}
                        height={300}
                        alt="iPhone showing next task notification"
                        className="rounded-lg mx-auto"
                      />
                    </div>
                  </div>
                </div>

                {/* Feature 5: Adaptive Methods */}
                <div className="bg-[#F6F5F4] rounded-xl p-8">
                  <div className="space-y-6 text-center">
                    {/* Feature Badge and Icon */}
                    <div className="flex items-center justify-center gap-3">
                      <div className="p-3 rounded-full bg-secondary/10">
                        <Puzzle className="h-6 w-6 text-secondary" />
                      </div>
                      <Badge variant="secondary" className="bg-tertiary/20 text-tertiary-foreground">
                        Adaptive Methods
                      </Badge>
                    </div>

                    {/* Sub-headline */}
                    <h4 className="text-xl md:text-2xl font-bold text-gray-900">
                      Your Way, Your Methods
                    </h4>

                    {/* Feature Description */}
                    <div className="space-y-3">
                      <p className="text-base text-gray-700 leading-relaxed">
                        Every ADHDer has their own unique ways to cope and thrive. Make My Days supports different productivity strategies through customizable plugins.
                      </p>
                      <p className="text-base text-gray-700 leading-relaxed">
                        Whether you use Spoon Theory, Eat the Frog, or Weekly Reviews, our system adapts to your preferred methods.
                      </p>
                    </div>

                    {/* Feature Image */}
                    <div className="mt-6">
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="w-16 h-16 bg-primary/20 rounded-lg flex items-center justify-center mx-auto">🥄</div>
                        <div className="w-16 h-16 bg-secondary/20 rounded-lg flex items-center justify-center mx-auto">🐸</div>
                        <div className="w-16 h-16 bg-tertiary/20 rounded-lg flex items-center justify-center mx-auto">📅</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="waitlist" className="w-full py-16 md:py-24 bg-gradient-to-r from-primary to-primary/80">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center space-y-8 text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold text-white">Ready to Make Your Days?</h2>
              <p className="text-lg text-white/90">Join thousands of ADHDers who are taking control of their time</p>

              <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <Input type="email" placeholder="Enter your email" className="flex-1 bg-white" />
                <Button className="bg-white text-primary hover:bg-gray-100 px-8">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <p className="text-sm text-white/80">No spam, just updates on our launch. Unsubscribe anytime.</p>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full py-12 bg-gray-50 border-t">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-center">
                <Image src="/images/logo.png" alt="Make My Days Logo" width={24} height={24} className="h-6 w-6" />
                <span className="ml-2 text-lg font-bold text-gray-900">Make My Days</span>
              </div>
              <p className="text-sm text-gray-600">Time management designed for the ADHD brain</p>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Connect</h4>
              <div className="space-y-2 text-sm">
                <Link href="#waitlist" className="block text-gray-600 hover:text-primary">
                  Join Waitlist
                </Link>
                <Link href="mailto:<EMAIL>" className="block text-gray-600 hover:text-primary">
                  Contact Us
                </Link>
                <Link href="#" className="block text-gray-600 hover:text-primary">
                  Updates
                </Link>
              </div>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200 text-center">
            <p className="text-sm text-gray-600">
              © {new Date().getFullYear()} Make My Days. All rights reserved. Made with ❤️ by ADHDers, for ADHDers.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
